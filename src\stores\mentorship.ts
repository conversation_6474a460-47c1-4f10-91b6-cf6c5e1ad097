/**
 * Mentorship Store
 *
 * This store manages all mentorship-related state and operations.
 * It provides actions for mentorship requests, sessions, and notifications.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import { useNotificationStore } from './notifications'
import { useAuthStore } from './auth'
import {
  MentorshipRequest,
  MentorshipSession,
  createMentorshipRequest,
  getMentorRequests,
  getMenteeRequests,
  respondToMentorshipRequest,
  createMentorshipSession,
  getUpcomingSessions
} from '@/services/mentorshipService'
import { 
  sendMentorshipRequestNotification,
  sendMentorshipRequestResponseNotification,
  sendMentorshipSessionReminderNotifications,
  sendMentorshipEventAnnouncementNotification
} from '@/services/mentorshipNotificationService'

export const useMentorshipStore = defineStore('mentorship', () => {
  // Dependencies
  const notificationStore = useNotificationStore()
  const authStore = useAuthStore()

  // State
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Mentorship Requests
  const mentorshipRequests = ref<MentorshipRequest[]>([])
  const sentRequests = ref<MentorshipRequest[]>([])
  const receivedRequests = ref<MentorshipRequest[]>([])
  
  // Mentorship Sessions
  const mentorshipSessions = ref<MentorshipSession[]>([])
  const upcomingSessions = ref<MentorshipSession[]>([])
  
  // Mentorship Events (placeholder for future implementation)
  const mentorshipEvents = ref<any[]>([])

  // Computed
  const pendingRequests = computed(() => 
    receivedRequests.value.filter(req => req.status === 'pending')
  )

  const acceptedRequests = computed(() => 
    mentorshipRequests.value.filter(req => req.status === 'accepted')
  )

  const todaysSessions = computed(() => {
    const today = new Date().toDateString()
    return upcomingSessions.value.filter(session => 
      new Date(session.scheduled_start_time).toDateString() === today
    )
  })

  // Actions

  /**
   * Create a new mentorship request
   */
  async function createRequest(requestData: Partial<MentorshipRequest>): Promise<boolean> {
    if (!authStore.user) {
      notificationStore.error('You must be logged in to create a mentorship request')
      return false
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: requestError } = await createMentorshipRequest({
        ...requestData,
        mentee_id: authStore.user.id
      })

      if (requestError) {
        throw requestError
      }

      if (data) {
        // Add to sent requests
        sentRequests.value.unshift(data)
        
        // Send notification to mentor
        await sendMentorshipRequestNotification({
          mentorId: data.mentor_id,
          menteeId: data.mentee_id,
          requestId: data.id,
          requestTitle: data.title,
          requestMessage: data.message
        })

        notificationStore.success('Mentorship request sent successfully!')
        return true
      }

      return false
    } catch (err: any) {
      console.error('Error creating mentorship request:', err)
      error.value = err.message || 'Failed to create mentorship request'
      notificationStore.error(error.value)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * Respond to a mentorship request
   */
  async function respondToRequest(
    requestId: string,
    status: 'accepted' | 'declined',
    response?: string
  ): Promise<boolean> {
    if (!authStore.user) {
      notificationStore.error('You must be logged in to respond to requests')
      return false
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: updateError } = await respondToMentorshipRequest(
        requestId,
        status,
        response
      )

      if (updateError) {
        throw updateError
      }

      if (data) {
        // Update local state
        const requestIndex = receivedRequests.value.findIndex(req => req.id === requestId)
        if (requestIndex !== -1) {
          receivedRequests.value[requestIndex] = data
        }

        // Send notification to mentee
        await sendMentorshipRequestResponseNotification({
          menteeId: data.mentee_id,
          mentorId: data.mentor_id,
          requestId: data.id,
          requestTitle: data.title,
          responseStatus: status,
          mentorResponse: response
        })

        notificationStore.success(`Request ${status} successfully!`)
        return true
      }

      return false
    } catch (err: any) {
      console.error('Error responding to mentorship request:', err)
      error.value = err.message || 'Failed to respond to request'
      notificationStore.error(error.value)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch mentorship requests for the current user
   */
  async function fetchRequests(): Promise<void> {
    if (!authStore.user) return

    loading.value = true
    error.value = null

    try {
      // Fetch both sent and received requests
      const [mentorResult, menteeResult] = await Promise.all([
        getMentorRequests(authStore.user.id),
        getMenteeRequests(authStore.user.id)
      ])

      if (mentorResult.error || menteeResult.error) {
        throw mentorResult.error || menteeResult.error
      }

      // Combine and store all requests
      const allRequests = [
        ...(mentorResult.data || []),
        ...(menteeResult.data || [])
      ]

      mentorshipRequests.value = allRequests
      sentRequests.value = menteeResult.data || []
      receivedRequests.value = mentorResult.data || []
    } catch (err: any) {
      console.error('Error fetching mentorship requests:', err)
      error.value = err.message || 'Failed to fetch requests'
      notificationStore.error(error.value)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch mentorship sessions for the current user
   */
  async function fetchSessions(): Promise<void> {
    if (!authStore.user) return

    loading.value = true
    error.value = null

    try {
      const { data, error: fetchError } = await getUpcomingSessions(authStore.user.id)

      if (fetchError) {
        throw fetchError
      }

      if (data) {
        mentorshipSessions.value = data

        // Filter upcoming sessions
        const now = new Date()
        upcomingSessions.value = data.filter(session =>
          new Date(session.scheduled_start_time) > now
        )
      }
    } catch (err: any) {
      console.error('Error fetching mentorship sessions:', err)
      error.value = err.message || 'Failed to fetch sessions'
      notificationStore.error(error.value)
    } finally {
      loading.value = false
    }
  }

  /**
   * Create a new mentorship session
   */
  async function createSession(sessionData: Partial<MentorshipSession>): Promise<boolean> {
    if (!authStore.user) {
      notificationStore.error('You must be logged in to create a session')
      return false
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: sessionError } = await createMentorshipSession(sessionData)

      if (sessionError) {
        throw sessionError
      }

      if (data) {
        mentorshipSessions.value.unshift(data)
        
        // Add to upcoming sessions if it's in the future
        if (new Date(data.scheduled_start_time) > new Date()) {
          upcomingSessions.value.unshift(data)
        }

        notificationStore.success('Mentorship session created successfully!')
        return true
      }

      return false
    } catch (err: any) {
      console.error('Error creating mentorship session:', err)
      error.value = err.message || 'Failed to create session'
      notificationStore.error(error.value)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * Send session reminder notifications
   */
  async function sendSessionReminders(sessionId: string): Promise<boolean> {
    const session = mentorshipSessions.value.find(s => s.id === sessionId)
    if (!session) {
      notificationStore.error('Session not found')
      return false
    }

    try {
      const success = await sendMentorshipSessionReminderNotifications({
        mentorId: session.mentor_id,
        menteeId: session.mentee_id,
        sessionId: session.id,
        sessionTitle: session.title,
        scheduledStartTime: session.scheduled_start_time,
        meetingLink: session.meeting_link,
        meetingPlatform: session.meeting_platform || 'video-call'
      })

      if (success) {
        notificationStore.success('Session reminders sent successfully!')
      } else {
        notificationStore.error('Failed to send session reminders')
      }

      return success
    } catch (err: any) {
      console.error('Error sending session reminders:', err)
      notificationStore.error('Failed to send session reminders')
      return false
    }
  }

  /**
   * Initialize the store
   */
  async function initialize(): Promise<void> {
    if (!authStore.user) return

    try {
      await Promise.all([
        fetchRequests(),
        fetchSessions()
      ])
    } catch (err: any) {
      console.error('Error initializing mentorship store:', err)
    }
  }

  return {
    // State
    loading,
    error,
    mentorshipRequests,
    sentRequests,
    receivedRequests,
    mentorshipSessions,
    upcomingSessions,
    mentorshipEvents,

    // Computed
    pendingRequests,
    acceptedRequests,
    todaysSessions,

    // Actions
    createRequest,
    respondToRequest,
    fetchRequests,
    fetchSessions,
    createSession,
    sendSessionReminders,
    initialize
  }
})
