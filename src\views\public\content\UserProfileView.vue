<template>
  <q-page class="user-profile-view">
    <div class="container q-mx-auto q-pa-md">
      <div class="row justify-center">
        <div class="col-12 col-md-8">
          <!-- Back Button -->
          <div class="q-mb-md">
            <q-btn
              flat
              color="primary"
              icon="arrow_back"
              label="Back"
              @click="goBack"
            />
          </div>

          <!-- Unified Profile View -->
          <unified-profile-view
            v-if="userId"
            :profile-id="userId"
            context="public"
            :is-current-user="isCurrentUser"
            :show-interactions="!isCurrentUser"
            :show-activity-feed="true"
            :show-debug-info="false"
            @message="handleMessage"
            @connect="handleConnect"
            @request-mentorship="handleRequestMentorship"
          />
        </div>
      </div>
    </div>

    <!-- Message Dialog -->
    <message-dialog
      v-if="showMessageDialog"
      :user-id="userId"
      :user-name="userName"
      @close="showMessageDialog = false"
    />

    <!-- Mentorship Request Dialog -->
    <q-dialog v-model="showMentorshipDialog" persistent>
      <mentorship-request-form
        v-if="mentorshipTargetId"
        :mentor-id="mentorshipTargetId"
        :mentor-name="mentorshipTargetName"
        @success="handleMentorshipSuccess"
        @cancel="showMentorshipDialog = false"
      />
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../../stores/auth'
import { useConnectionService } from '../../../services/connectionService'
import { useMessagingStore } from '../../../stores/messaging'
import { useNotificationStore } from '../../../stores/notifications'
import { getUniversalUsername } from '../../../utils/userUtils'
import UnifiedProfileView from '../../../components/profile/UnifiedProfileView.vue'
import MessageDialog from '../../../components/messaging/MessageDialog.vue'
import MentorshipRequestForm from '../../../components/mentorship/MentorshipRequestForm.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const connectionService = useConnectionService()
const messagingStore = useMessagingStore()
const notifications = useNotificationStore()

// State
const showMessageDialog = ref(false)
const showMentorshipDialog = ref(false)
const mentorshipTargetId = ref<string | null>(null)
const mentorshipTargetName = ref<string>('')

// Computed
const userId = computed(() => {
  const id = route.params.id as string
  console.log('UserProfileView: Route params.id:', id)
  console.log('UserProfileView: Full route params:', route.params)
  console.log('UserProfileView: Route path:', route.path)
  console.log('UserProfileView: Route name:', route.name)
  return id
})
const isCurrentUser = computed(() => {
  const isCurrent = authStore.currentUser?.id === userId.value
  console.log('UserProfileView: isCurrentUser check:', {
    currentUserId: authStore.currentUser?.id,
    profileUserId: userId.value,
    isCurrentUser: isCurrent
  })
  return isCurrent
})
const userName = computed(() => 'User') // Will be populated by the unified component

// Methods
function goBack() {
  router.back()
}

async function handleMessage(targetUserId: string) {
  try {
    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      notifications.warning('Please sign in to send messages')
      router.push({ name: 'sign-in', query: { redirect: route.fullPath } })
      return
    }

    // Initialize messaging store
    await messagingStore.initializeMessaging()

    // Show the message dialog
    showMessageDialog.value = true
  } catch (error) {
    console.error('Error opening message dialog:', error)
    notifications.error('Failed to open message dialog')
  }
}

async function handleConnect(targetUserId: string) {
  try {
    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      notifications.warning('Please sign in to connect with users')
      router.push({ name: 'sign-in', query: { redirect: route.fullPath } })
      return
    }

    // Send connection request
    await connectionService.connectWithUser(targetUserId)
    notifications.success('Connection request sent!')
  } catch (error) {
    console.error('Error sending connection request:', error)
    notifications.error('Failed to send connection request')
  }
}

function handleRequestMentorship(targetUserId: string) {
  console.log('UserProfileView: Handling mentorship request for user:', targetUserId)

  if (!authStore.currentUser) {
    notifications.warning('Please sign in to request mentorship')
    router.push({ name: 'sign-in', query: { redirect: route.fullPath } })
    return
  }

  mentorshipTargetId.value = targetUserId
  mentorshipTargetName.value = 'Mentor' // This will be updated by the form component
  showMentorshipDialog.value = true
}

function handleMentorshipSuccess(requestId: string) {
  console.log('UserProfileView: Mentorship request sent successfully:', requestId)
  showMentorshipDialog.value = false
  mentorshipTargetId.value = null
  mentorshipTargetName.value = ''

  notifications.success('Mentorship request sent successfully!')
}

// Lifecycle
onMounted(() => {
  console.log('UserProfileView: Component mounted')
  console.log('UserProfileView: Route params on mount:', route.params)
  console.log('UserProfileView: User ID on mount:', userId.value)
  console.log('UserProfileView: Route path on mount:', route.path)
  console.log('UserProfileView: Route name on mount:', route.name)

  // Test if we can load the profile directly
  if (userId.value) {
    console.log('UserProfileView: Testing direct profile load for ID:', userId.value)
  }
})
</script>

<style scoped>
.user-profile-view {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
}

@media (max-width: 767px) {
  .user-profile-view {
    padding: 8px;
  }
}
</style>