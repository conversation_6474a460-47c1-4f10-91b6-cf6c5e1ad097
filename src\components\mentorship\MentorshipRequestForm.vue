<template>
  <q-card class="mentorship-request-form">
    <!-- Fixed Header -->
    <q-card-section class="form-header bg-gradient text-white">
      <div class="text-h6">
        <q-icon name="school" class="q-mr-sm" />
        Request Mentorship
      </div>
      <div class="text-subtitle2">Send a mentorship request to {{ mentorName }}</div>
    </q-card-section>

    <!-- Scrollable Form Content -->
    <q-scroll-area class="form-content">
      <q-form @submit="submitRequest" class="q-pa-md">
      <div class="q-gutter-md">
        <!-- Request Type -->
        <q-select
          v-model="form.request_type"
          :options="requestTypeOptions"
          label="Request Type"
          outlined
          required
          emit-value
          map-options
        />

        <!-- Title -->
        <q-input
          v-model="form.title"
          label="Request Title"
          outlined
          required
          placeholder="e.g., Career guidance in tech entrepreneurship"
          maxlength="255"
        />

        <!-- Description -->
        <q-input
          v-model="form.description"
          label="Description"
          type="textarea"
          outlined
          required
          rows="4"
          placeholder="Describe what you're looking for, your background, and specific areas where you need guidance..."
          maxlength="1000"
        />

        <!-- Preferences Row -->
        <div class="row q-gutter-md">
          <div class="col">
            <q-select
              v-model="form.preferred_duration"
              :options="durationOptions"
              label="Preferred Duration"
              outlined
              emit-value
              map-options
            />
          </div>
          <div class="col">
            <q-select
              v-model="form.preferred_frequency"
              :options="frequencyOptions"
              label="Preferred Frequency"
              outlined
              emit-value
              map-options
            />
          </div>
        </div>

        <!-- Meeting Type and Urgency -->
        <div class="row q-gutter-md">
          <div class="col">
            <q-select
              v-model="form.preferred_format"
              :options="formatOptions"
              label="Preferred Format"
              outlined
              emit-value
              map-options
            />
          </div>

        </div>

        <!-- Goals -->
        <q-input
          v-model="goalsText"
          label="Your Goals (comma-separated)"
          outlined
          placeholder="e.g., Launch a startup, Improve leadership skills, Build a network"
        />
      </div>
      </q-form>
    </q-scroll-area>

    <!-- Fixed Footer -->
    <q-card-actions class="form-footer bg-white q-pa-md">
      <q-space />
      <q-btn
        flat
        color="grey"
        @click="$emit('cancel')"
        :disable="loading"
        class="q-mr-sm"
      >
        Cancel
      </q-btn>
      <q-btn
        @click="submitRequest"
        color="primary"
        :loading="loading"
        :disable="!isFormValid"
      >
        Send Request
      </q-btn>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import { createMentorshipRequest } from '../../services/mentorshipService'
import { useAuthStore } from '../../stores/auth'

const props = defineProps<{
  mentorId: string
  mentorName: string
}>()

const emit = defineEmits<{
  success: [requestId: string]
  cancel: []
}>()

const $q = useQuasar()
const authStore = useAuthStore()

const loading = ref(false)
const goalsText = ref('')
const areasText = ref('')

const form = ref({
  title: '',
  description: '',
  preferred_duration: '1-3 months',
  preferred_frequency: 'Weekly',
  preferred_format: 'Video calls'
})



const durationOptions = [
  { label: '1-3 months', value: '1-3 months' },
  { label: '3-6 months', value: '3-6 months' },
  { label: '6-12 months', value: '6-12 months' },
  { label: 'Ongoing', value: 'Ongoing' },
  { label: 'Project-based', value: 'Project-based' }
]

const frequencyOptions = [
  { label: 'Weekly', value: 'Weekly' },
  { label: 'Bi-weekly', value: 'Bi-weekly' },
  { label: 'Monthly', value: 'Monthly' },
  { label: 'As needed', value: 'As needed' },
  { label: 'One-time', value: 'One-time' }
]

const formatOptions = [
  { label: 'Video calls', value: 'Video calls' },
  { label: 'Phone calls', value: 'Phone calls' },
  { label: 'In-person meetings', value: 'In-person meetings' },
  { label: 'Email/messaging', value: 'Email/messaging' },
  { label: 'Mixed format', value: 'Mixed format' }
]



const isFormValid = computed(() => {
  return form.value.title.trim() && form.value.description.trim()
})

async function submitRequest() {
  if (!authStore.user) {
    $q.notify({
      type: 'negative',
      message: 'You must be logged in to send a mentorship request'
    })
    return
  }

  loading.value = true

  try {
    const requestData = {
      mentor_id: props.mentorId,
      mentee_id: authStore.user.id,
      ...form.value,
      goals: goalsText.value || ''
    }

    const { data, error } = await createMentorshipRequest(requestData)

    if (error) {
      throw error
    }

    $q.notify({
      type: 'positive',
      message: 'Mentorship request sent successfully!',
      caption: 'The mentor will be notified and can respond within 7 days.'
    })

    emit('success', data!.id)
  } catch (error: any) {
    console.error('Error sending mentorship request:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to send mentorship request',
      caption: error.message || 'Please try again later'
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.mentorship-request-form {
  max-width: 600px;
  margin: 0 auto;
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.form-header {
  flex-shrink: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border-bottom: 1px solid #e2e8f0;
}

.form-content {
  flex: 1;
  overflow-y: auto;
}

.form-footer {
  flex-shrink: 0;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.bg-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

/* Ensure form content has proper spacing */
.form-content .q-form {
  min-height: 100%;
}

/* Mobile responsiveness */
@media (max-width: 600px) {
  .mentorship-request-form {
    height: 90vh;
    max-width: 100%;
  }

  .form-header {
    padding: 16px;
  }

  .form-footer {
    padding: 12px 16px;
  }
}
</style>
