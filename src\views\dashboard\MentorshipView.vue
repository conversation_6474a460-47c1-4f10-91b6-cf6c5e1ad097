<template>
  <div class="mentorship-view">
    <!-- Header -->
    <div class="page-header q-pa-lg">
      <div class="row items-center justify-between">
        <div>
          <h1 class="text-h4 text-weight-bold q-mb-sm">
            <q-icon name="psychology" class="q-mr-sm text-primary" />
            Mentorship Hub
          </h1>
          <p class="text-subtitle1 text-grey-7">
            Connect with mentors and mentees to grow your innovation journey
          </p>
        </div>
        <div class="q-gutter-sm">
          <q-btn
            v-if="authStore.user?.profile_type === 'mentor'"
            color="primary"
            icon="add"
            label="Create Event"
            @click="showCreateEvent = true"
          />
          <q-btn
            color="secondary"
            icon="search"
            label="Find Mentors"
            to="/virtual-community?filter=mentor"
          />
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="row q-gutter-md q-pa-lg">
      <div class="col-12 col-md-3">
        <q-card class="stat-card">
          <q-card-section class="text-center">
            <q-icon name="pending" size="2rem" color="orange" />
            <div class="text-h6 q-mt-sm">{{ mentorshipStore.pendingRequests.length }}</div>
            <div class="text-caption text-grey-7">Pending Requests</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="stat-card">
          <q-card-section class="text-center">
            <q-icon name="check_circle" size="2rem" color="green" />
            <div class="text-h6 q-mt-sm">{{ mentorshipStore.acceptedRequests.length }}</div>
            <div class="text-caption text-grey-7">Active Mentorships</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="stat-card">
          <q-card-section class="text-center">
            <q-icon name="event" size="2rem" color="blue" />
            <div class="text-h6 q-mt-sm">{{ mentorshipStore.upcomingSessions.length }}</div>
            <div class="text-caption text-grey-7">Upcoming Sessions</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="stat-card">
          <q-card-section class="text-center">
            <q-icon name="today" size="2rem" color="purple" />
            <div class="text-h6 q-mt-sm">{{ mentorshipStore.todaysSessions.length }}</div>
            <div class="text-caption text-grey-7">Today's Sessions</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Main Content -->
    <div class="row q-gutter-lg q-pa-lg">
      <!-- Left Column - Requests -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">
              <q-icon name="inbox" class="q-mr-sm" />
              Mentorship Requests
            </div>
            
            <q-tabs v-model="requestTab" class="text-grey" active-color="primary" indicator-color="primary">
              <q-tab name="received" label="Received" />
              <q-tab name="sent" label="Sent" />
            </q-tabs>

            <q-separator />

            <q-tab-panels v-model="requestTab" animated>
              <q-tab-panel name="received">
                <div v-if="mentorshipStore.receivedRequests.length === 0" class="text-center q-pa-lg">
                  <q-icon name="inbox" size="3rem" color="grey-5" />
                  <div class="text-h6 q-mt-md text-grey-7">No requests received</div>
                  <div class="text-body2 text-grey-6">Mentorship requests will appear here</div>
                </div>
                <div v-else class="q-gutter-sm">
                  <q-card
                    v-for="request in mentorshipStore.receivedRequests"
                    :key="request.id"
                    flat
                    bordered
                    class="request-card"
                  >
                    <q-card-section>
                      <div class="row items-start justify-between">
                        <div class="col">
                          <div class="text-subtitle1 text-weight-medium">{{ request.title }}</div>
                          <div class="text-body2 text-grey-7 q-mt-xs">{{ request.message }}</div>
                          <div class="text-caption text-grey-6 q-mt-sm">
                            {{ formatDate(request.created_at) }}
                          </div>
                        </div>
                        <div class="col-auto">
                          <q-chip
                            :color="getStatusColor(request.status)"
                            text-color="white"
                            :label="request.status"
                            size="sm"
                          />
                        </div>
                      </div>
                      <div v-if="request.status === 'pending'" class="q-mt-md">
                        <q-btn
                          color="positive"
                          label="Accept"
                          size="sm"
                          @click="respondToRequest(request.id, 'accepted')"
                          class="q-mr-sm"
                        />
                        <q-btn
                          color="negative"
                          label="Decline"
                          size="sm"
                          outline
                          @click="respondToRequest(request.id, 'declined')"
                        />
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </q-tab-panel>

              <q-tab-panel name="sent">
                <div v-if="mentorshipStore.sentRequests.length === 0" class="text-center q-pa-lg">
                  <q-icon name="send" size="3rem" color="grey-5" />
                  <div class="text-h6 q-mt-md text-grey-7">No requests sent</div>
                  <div class="text-body2 text-grey-6">Your sent requests will appear here</div>
                </div>
                <div v-else class="q-gutter-sm">
                  <q-card
                    v-for="request in mentorshipStore.sentRequests"
                    :key="request.id"
                    flat
                    bordered
                    class="request-card"
                  >
                    <q-card-section>
                      <div class="row items-start justify-between">
                        <div class="col">
                          <div class="text-subtitle1 text-weight-medium">{{ request.title }}</div>
                          <div class="text-body2 text-grey-7 q-mt-xs">{{ request.message }}</div>
                          <div class="text-caption text-grey-6 q-mt-sm">
                            {{ formatDate(request.created_at) }}
                          </div>
                        </div>
                        <div class="col-auto">
                          <q-chip
                            :color="getStatusColor(request.status)"
                            text-color="white"
                            :label="request.status"
                            size="sm"
                          />
                        </div>
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </q-tab-panel>
            </q-tab-panels>
          </q-card-section>
        </q-card>
      </div>

      <!-- Right Column - Sessions -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">
              <q-icon name="event" class="q-mr-sm" />
              Upcoming Sessions
            </div>
            
            <div v-if="mentorshipStore.upcomingSessions.length === 0" class="text-center q-pa-lg">
              <q-icon name="event_available" size="3rem" color="grey-5" />
              <div class="text-h6 q-mt-md text-grey-7">No upcoming sessions</div>
              <div class="text-body2 text-grey-6">Scheduled sessions will appear here</div>
            </div>
            <div v-else class="q-gutter-sm">
              <q-card
                v-for="session in mentorshipStore.upcomingSessions"
                :key="session.id"
                flat
                bordered
                class="session-card"
              >
                <q-card-section>
                  <div class="text-subtitle1 text-weight-medium">{{ session.title }}</div>
                  <div class="text-body2 text-grey-7 q-mt-xs">{{ session.description }}</div>
                  <div class="row items-center q-mt-sm q-gutter-sm">
                    <q-icon name="schedule" size="sm" color="grey-6" />
                    <span class="text-caption text-grey-6">
                      {{ formatDateTime(session.scheduled_start_time) }}
                    </span>
                  </div>
                  <div class="row items-center q-mt-xs q-gutter-sm">
                    <q-icon name="videocam" size="sm" color="grey-6" />
                    <span class="text-caption text-grey-6">{{ session.meeting_platform }}</span>
                  </div>
                  <div class="q-mt-md">
                    <q-btn
                      v-if="session.meeting_link"
                      color="primary"
                      label="Join Meeting"
                      size="sm"
                      :href="session.meeting_link"
                      target="_blank"
                      class="q-mr-sm"
                    />
                    <q-btn
                      color="grey-7"
                      label="Send Reminder"
                      size="sm"
                      outline
                      @click="sendReminder(session.id)"
                    />
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Loading Overlay -->
    <q-inner-loading :showing="mentorshipStore.loading">
      <q-spinner-gears size="50px" color="primary" />
    </q-inner-loading>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useMentorshipStore } from '@/stores/mentorship'
import { useAuthStore } from '@/stores/auth'

const $q = useQuasar()
const mentorshipStore = useMentorshipStore()
const authStore = useAuthStore()

// State
const requestTab = ref('received')
const showCreateEvent = ref(false)

// Methods
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString()
}

function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleString()
}

function getStatusColor(status: string): string {
  const colors: Record<string, string> = {
    pending: 'orange',
    accepted: 'green',
    declined: 'red',
    withdrawn: 'grey',
    expired: 'grey-7'
  }
  return colors[status] || 'grey'
}

async function respondToRequest(requestId: string, status: 'accepted' | 'declined') {
  const success = await mentorshipStore.respondToRequest(requestId, status)
  if (success) {
    $q.notify({
      type: 'positive',
      message: `Request ${status} successfully!`
    })
  }
}

async function sendReminder(sessionId: string) {
  const success = await mentorshipStore.sendSessionReminders(sessionId)
  if (success) {
    $q.notify({
      type: 'positive',
      message: 'Reminder sent successfully!'
    })
  }
}

// Lifecycle
onMounted(async () => {
  await mentorshipStore.initialize()
})
</script>

<style scoped>
.mentorship-view {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card {
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.request-card,
.session-card {
  transition: all 0.2s;
}

.request-card:hover,
.session-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
