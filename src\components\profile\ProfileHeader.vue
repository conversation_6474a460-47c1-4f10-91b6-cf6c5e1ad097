<template>
  <div class="twitter-profile-header">
    <!-- Cover Photo Area -->
    <div class="cover-photo-section">
      <div class="cover-photo-overlay"></div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
      <!-- Avatar positioned to overlap cover -->
      <div class="avatar-section">
        <q-avatar size="140px" class="profile-avatar">
          <img v-if="profile.avatar_url" :src="profile.avatar_url" alt="Profile Avatar">
          <user-avatar
            v-else
            :name="profile.displayName"
            :email="profile.email"
            :user-id="profile.user_id || profile.id"
            size="140px"
            :clickable="false"
          />
        </q-avatar>
      </div>

      <!-- Profile Info Section -->
      <div class="profile-info-section">
        <div class="profile-header-row">
          <div class="profile-main-info">
            <h1 class="profile-name">{{ profile.displayName || getDisplayName() }}</h1>
            <div class="profile-handle">@{{ getUsername() }}</div>
          </div>

          <!-- Top Action Buttons (Edit for current user) -->
          <div v-if="isCurrentUser && context === 'dashboard'" class="top-actions">
            <q-btn
              outline
              rounded
              color="primary"
              icon="edit"
              label="Edit Profile"
              :to="{ name: 'profile-edit', params: { id: profile.user_id || profile.id } }"
              class="edit-btn"
            />
          </div>
        </div>

        <!-- Profile Type Badge -->
        <div class="profile-badges">
          <q-chip
            :color="getProfileTypeColor()"
            text-color="white"
            :icon="getProfileTypeIcon()"
            class="profile-type-chip"
          >
            {{ profile.formattedProfileType || formatProfileType(profile.profile_type) }}
          </q-chip>
        </div>

        <!-- Bio/Description -->
        <div v-if="profile.bio || profile.summary" class="profile-bio">
          {{ profile.bio || profile.summary }}
        </div>

        <!-- Profile Meta Info -->
        <div class="profile-meta-row">
          <div v-if="getLocation()" class="meta-item">
            <q-icon name="location_on" size="sm" />
            <span>{{ getLocation() }}</span>
          </div>
          <div v-if="profile.created_at" class="meta-item">
            <q-icon name="event" size="sm" />
            <span>Joined {{ formatDate(profile.created_at) }}</span>
          </div>
          <div v-if="profile.email && context === 'dashboard'" class="meta-item">
            <q-icon name="email" size="sm" />
            <span>{{ profile.email }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Action Buttons (Twitter-style overflow) -->
    <div v-if="showInteractions && !isCurrentUser" class="floating-actions">
      <!-- Message Button -->
      <q-btn
        fab
        color="primary"
        icon="message"
        @click="$emit('message', profile.user_id || profile.id)"
        class="action-fab message-fab"
      >
        <q-tooltip>Send Message</q-tooltip>
      </q-btn>

      <!-- Connect Button -->
      <q-btn
        fab
        :key="`connect-${userId}-${connectionButton.connectionStatus.value}`"
        :color="getConnectButtonColor()"
        :icon="connectionButton.buttonConfig.value.icon"
        :disabled="connectionButton.buttonConfig.value.disabled"
        :loading="connectionButton.buttonConfig.value.loading"
        @click="connectionButton.handleConnect"
        class="action-fab connect-fab"
      >
        <q-tooltip>{{ connectionButton.tooltipText.value }}</q-tooltip>
      </q-btn>

      <!-- Request Mentorship Button (for mentors only) -->
      <q-btn
        v-if="profile.profile_type === 'mentor'"
        fab
        color="purple"
        icon="school"
        @click="$emit('requestMentorship', profile.user_id || profile.id)"
        class="action-fab mentorship-fab"
      >
        <q-tooltip>Request Mentorship</q-tooltip>
      </q-btn>
    </div>

    <!-- Profile Completion (for current user) -->
    <div v-if="isCurrentUser" class="profile-completion-section">
      <q-card class="completion-card">
        <q-card-section class="q-pa-md">
          <div class="completion-header">
            <div class="completion-title">Profile Completion</div>
            <q-circular-progress
              :value="getCompletionPercentage()"
              size="60px"
              :thickness="0.15"
              :color="getCompletionColor()"
              track-color="grey-3"
              class="completion-circle"
            >
              <div class="completion-percentage">{{ getCompletionPercentage() }}%</div>
            </q-circular-progress>
          </div>

          <q-linear-progress
            :value="getCompletionPercentage() / 100"
            :color="getCompletionColor()"
            class="completion-bar"
            size="6px"
            rounded
          />

          <div class="completion-footer">
            <div class="completion-message">
              {{ getCompletionMessage() }}
            </div>
            <q-btn
              v-if="getCompletionPercentage() < 100"
              outline
              rounded
              color="primary"
              label="Complete Profile"
              size="sm"
              :to="{ name: 'profile-edit', params: { id: profile.user_id || profile.id } }"
              class="complete-btn"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup lang="ts">

import { date } from 'quasar'
import { formatProfileType } from '../../services/profileTypes'
import { getNameFromEmail } from '../../utils/nameUtils'
import { useConnectionButton } from '../../composables/useConnectionButton'
import UserAvatar from '../common/UserAvatar.vue'

// Props
const props = defineProps<{
  profile: any
  context: 'dashboard' | 'public'
  showInteractions?: boolean
  isCurrentUser?: boolean
}>()

// Emits
const emit = defineEmits<{
  message: [userId: string]
  connect: [userId: string]
  requestMentorship: [userId: string]
}>()

// Use unified connection button logic
const userId = props.profile.user_id || props.profile.id;
const connectionButton = useConnectionButton(userId);

// Debug logging
console.log('ProfileHeader: Initial connection button config:', {
  userId,
  showInteractions: props.showInteractions,
  isCurrentUser: props.isCurrentUser,
  buttonConfig: connectionButton.buttonConfig.value,
  tooltipText: connectionButton.tooltipText.value
});
function getDisplayName(): string {
  if (!props.profile) return 'Unknown User'

  const fullName = `${props.profile.first_name || ''} ${props.profile.last_name || ''}`.trim()
  if (fullName) return fullName

  if (props.profile.profile_name) return props.profile.profile_name
  if (props.profile.email) return getNameFromEmail(props.profile.email)

  // For profiles with minimal data, show a more user-friendly message
  return 'User Profile'
}

function getLocation(): string {
  if (!props.profile) return ''
  
  const locations = [
    props.profile.location,
    props.profile.city,
    props.profile.state_province,
    props.profile.country
  ].filter(Boolean)
  
  return locations.join(', ')
}

function getBio(): string {
  if (!props.profile) return ''
  
  return props.profile.bio || 
         props.profile.about || 
         props.profile.description || 
         ''
}

function formatDate(dateString: string): string {
  if (!dateString) return ''

  try {
    return date.formatDate(new Date(dateString), 'MMMM YYYY')
  } catch {
    return ''
  }
}

function getConnectButtonColor(): string {
  const configColor = connectionButton.buttonConfig.value.color

  // Map the connection button colors to high-contrast colors
  switch (configColor) {
    case 'green':
      return 'positive' // Dark green
    case 'orange':
      return 'warning' // Orange
    case 'blue':
      return 'info' // Blue
    case 'grey':
    case 'gray':
      return 'dark' // Dark grey
    default:
      return 'positive' // Default to dark green
  }
}

function getCompletionPercentage(): number {
  if (!props.profile) return 0
  return Math.round(props.profile.profile_completion || 0)
}

function getCompletionColor(): string {
  const completion = getCompletionPercentage()
  if (completion < 30) return 'red'
  if (completion < 70) return 'orange'
  if (completion < 100) return 'blue'
  return 'green'
}

function getCompletionMessage(): string {
  const percentage = getCompletionPercentage()
  if (percentage === 0) return 'Get started by completing your profile'
  if (percentage < 30) return 'Just getting started! Add more details'
  if (percentage < 70) return 'Good progress! Keep going'
  if (percentage < 100) return 'Almost there! Just a few more fields'
  return 'Your profile is complete!'
}

// New methods for Twitter-style profile
function getUsername(): string {
  if (props.profile.username) return props.profile.username
  if (props.profile.email) return props.profile.email.split('@')[0]
  return 'user'
}

function getProfileTypeColor(): string {
  const type = props.profile.profile_type
  const colors: Record<string, string> = {
    'innovator': 'green',
    'investor': 'blue',
    'mentor': 'purple',
    'professional': 'teal',
    'industry_expert': 'orange',
    'academic_student': 'indigo',
    'academic_institution': 'deep-orange',
    'organisation': 'pink'
  }
  return colors[type] || 'grey'
}

function getProfileTypeIcon(): string {
  const type = props.profile.profile_type
  const icons: Record<string, string> = {
    'innovator': 'lightbulb',
    'investor': 'account_balance',
    'mentor': 'school',
    'professional': 'business',
    'industry_expert': 'engineering',
    'academic_student': 'school',
    'academic_institution': 'account_balance',
    'organisation': 'business'
  }
  return icons[type] || 'person'
}
</script>

<style scoped>
/* Twitter/X Style Profile Header */
.twitter-profile-header {
  position: relative;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

/* Cover Photo Section */
.cover-photo-section {
  height: 120px;
  background: linear-gradient(135deg, #dfefe6 0%, #c8e6d0 100%);
  position: relative;
}

.cover-photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
}

/* Profile Content */
.profile-content {
  position: relative;
  padding: 0 24px 24px;
}

/* Avatar Section */
.avatar-section {
  position: relative;
  margin-top: -70px;
  margin-bottom: 16px;
}

.profile-avatar {
  border: 4px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background: white;
}

/* Profile Info Section */
.profile-info-section {
  margin-top: 12px;
}

.profile-header-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.profile-main-info {
  flex: 1;
}

.profile-name {
  font-size: 2rem;
  font-weight: 800;
  line-height: 1.2;
  color: #0f1419;
  margin: 0;
}

.profile-handle {
  font-size: 1rem;
  color: #536471;
  margin-top: 4px;
}

.top-actions {
  margin-left: 16px;
}

.edit-btn {
  min-width: 120px;
  font-weight: 600;
}

/* Profile Badges */
.profile-badges {
  margin-bottom: 16px;
}

.profile-type-chip {
  font-weight: 600;
  font-size: 0.875rem;
}

/* Bio Section */
.profile-bio {
  font-size: 1rem;
  line-height: 1.5;
  color: #0f1419;
  margin-bottom: 16px;
}

/* Meta Info Row */
.profile-meta-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #536471;
  font-size: 0.875rem;
}

.meta-item .q-icon {
  color: #536471;
}

/* Floating Action Buttons */
.floating-actions {
  position: absolute;
  bottom: 24px;
  right: 24px;
  display: flex;
  gap: 12px;
  z-index: 10;
}

.action-fab {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.action-fab:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.message-fab {
  background: #1da1f2 !important;
}

.connect-fab {
  background: #17bf63 !important;
}

.mentorship-fab {
  background: #8b5cf6 !important;
}

/* Profile Completion Section */
.profile-completion-section {
  margin-top: 16px;
}

.completion-card {
  border-radius: 12px;
  border: 1px solid #e1e8ed;
  box-shadow: none;
}

.completion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.completion-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #0f1419;
}

.completion-circle {
  margin-left: 16px;
}

.completion-percentage {
  font-size: 0.75rem;
  font-weight: 600;
  color: #0f1419;
}

.completion-bar {
  margin-bottom: 16px;
}

.completion-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.completion-message {
  font-size: 0.875rem;
  color: #536471;
}

.complete-btn {
  font-weight: 600;
  min-width: 140px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .cover-photo-section {
    height: 90px;
  }

  .profile-content {
    padding: 0 16px 16px;
  }

  .avatar-section {
    margin-top: -50px;
  }

  .profile-avatar {
    width: 100px !important;
    height: 100px !important;
  }

  .profile-name {
    font-size: 1.5rem;
  }

  .profile-header-row {
    flex-direction: column;
    gap: 12px;
  }

  .top-actions {
    margin-left: 0;
    align-self: stretch;
  }

  .edit-btn {
    width: 100%;
  }

  .floating-actions {
    position: static;
    justify-content: center;
    margin-top: 16px;
  }

  .profile-meta-row {
    flex-direction: column;
    gap: 8px;
  }

  .completion-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .completion-footer {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .complete-btn {
    width: 100%;
  }
}
</style>
