<script setup lang="ts">
import { ref, computed } from 'vue';
import UnifiedIcon from '../ui/UnifiedIcon.vue';
import { useAuthStore } from '../../stores/auth';
import { useRouter } from 'vue-router';

const logoUrl = ref('/smile-factory-logo.svg');
const authStore = useAuthStore();
const router = useRouter();

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated);

const scrollToSignup = () => {
  const signupSection = document.getElementById('signup-section');
  if (signupSection) {
    signupSection.scrollIntoView({ behavior: 'smooth' });
  }
};

const goToDashboard = () => {
  router.push('/dashboard');
};
</script>

<template>
  <q-header class="bg-menu q-py-sm shadow-1">
    <q-toolbar>
      <div class="container q-mx-auto q-px-md">
        <div class="row">
          <div class="col-1" />
          <div class="col-10">
            <div class="row full-width items-center">
              <!-- Social Media Icons -->
              <div class="col-3 social-icons gt-sm">
                <q-btn flat round style="color: #0D8A3E">
                  <unified-icon name="alternate_email" />
                </q-btn>
                <q-btn flat round style="color: #0D8A3E">
                  <unified-icon name="photo_camera" />
                </q-btn>
                <q-btn flat round style="color: #0D8A3E">
                  <unified-icon name="work" />
                </q-btn>
              </div>

              <!-- Logo and Title - Centered -->
              <div class="col-6 flex justify-center items-center">
                <div class="logo-container flex items-center">
                  <div class="logo-img">
                    <img :src="logoUrl" alt="Smile Factory Logo" class="logo-image" style="height: 50px;">
                  </div>
                  <div class="q-ml-sm text-weight-bold logo-text" style="color: #0D8A3E">Smile Factory</div>
                </div>
              </div>

              <!-- Sign Up Button or Dashboard Button based on auth status -->
              <div class="col-3 flex justify-end">
                <template v-if="!isAuthenticated">
                  <q-btn outline rounded style="color: #0D8A3E; border-color: #0D8A3E" label="Sign Up" class="text-weight-medium q-px-md signup-btn" @click="scrollToSignup" />
                </template>
                <template v-else>
                  <q-btn outline rounded style="color: #0D8A3E; border-color: #0D8A3E" label="Dashboard" class="text-weight-medium q-px-md dashboard-btn" @click="goToDashboard" />
                </template>
              </div>
            </div>
          </div>
          <div class="col-1" />
        </div>
      </div>
    </q-toolbar>
  </q-header>
</template>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

.bg-menu {
  background-color: #dfefe6;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.social-icons {
  display: flex;
  align-items: center;
}

.social-icons .q-btn {
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.social-icons .q-btn:hover {
  opacity: 1;
}

.q-btn.outline {
  border: 2px solid;
}

@media (max-width: 599px) {
  .container {
    padding: 0 16px !important;
  }

  .col-1 {
    display: none;
  }

  .col-10 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .logo-text {
    font-size: 0.9rem;
  }

  .logo-image {
    height: 40px !important;
  }

  .col-6 {
    justify-content: flex-start !important;
    flex: 0 1 auto;
  }

  .col-3.flex.justify-end {
    margin-left: auto;
    flex: 0 0 auto;
  }

  .q-btn.outline {
    border-radius: 24px;
    padding: 8px 20px;
    font-weight: 500;
    background: white;
    white-space: nowrap;
  }

  .signup-btn {
    min-width: 110px;
  }
}
</style>
